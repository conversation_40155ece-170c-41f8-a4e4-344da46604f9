import React from 'react';
import { useTranslation } from 'react-i18next';
import { Phone, Mail, MapPin, ArrowUp } from 'lucide-react';

const Footer: React.FC = () => {
  const { t } = useTranslation();

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-12 grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <div className="text-3xl font-bold gradient-nova-text mb-4">
              Novalead
            </div>
            <p className="text-gray-300 mb-6 leading-relaxed max-w-md">
              {t('footer.description')}
            </p>
            <div className="space-y-3">
              <div className="flex items-center text-gray-300">
                <Phone size={18} className="mr-3 text-nova-red" />
                <span>+32 56 90 24 91</span>
              </div>
              <div className="flex items-center text-gray-300">
                <Mail size={18} className="mr-3 text-nova-blue" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center text-gray-300">
                <MapPin size={18} className="mr-3 text-nova-orange" />
                <span>Henri Lebbestraat 188, 8790 Waregem, België</span>
              </div>
              <div className="flex items-center text-gray-300">
                <MapPin size={18} className="mr-3 text-nova-orange" />
                <span>Les portes de Marrakech Zone46, Marrakech, Marokko</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{t('footer.quickLinks.title')}</h3>
            <ul className="space-y-2">
              <li>
                <button
                  onClick={() => scrollToSection('home')}
                  className="text-gray-300 hover:text-nova-red transition-colors duration-200"
                >
                  {t('footer.quickLinks.home')}
                </button>
              </li>
              <li>
                <button
                  onClick={() => scrollToSection('services')}
                  className="text-gray-300 hover:text-nova-red transition-colors duration-200"
                >
                  {t('footer.quickLinks.services')}
                </button>
              </li>
              <li>
                <button
                  onClick={() => scrollToSection('contact')}
                  className="text-gray-300 hover:text-nova-red transition-colors duration-200"
                >
                  {t('footer.quickLinks.contact')}
                </button>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-nova-red transition-colors duration-200">
                  {t('footer.quickLinks.aboutUs')}
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-nova-red transition-colors duration-200">
                  {t('footer.quickLinks.caseStudies')}
                </a>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{t('footer.ourServices.title')}</h3>
            <ul className="space-y-2 text-sm">
              <li className="text-gray-300">{t('footer.ourServices.leadGeneration')}</li>
              <li className="text-gray-300">{t('footer.ourServices.crossSelling')}</li>
              <li className="text-gray-300">{t('footer.ourServices.appointmentScheduling')}</li>
              <li className="text-gray-300">{t('footer.ourServices.retentionCalls')}</li>
              <li className="text-gray-300">{t('footer.ourServices.inboundTelemarketing')}</li>
              <li className="text-gray-300">{t('footer.ourServices.customerResearch')}</li>
              <li className="text-gray-300">{t('footer.ourServices.calendarManagement')}</li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 py-6 flex flex-col md:flex-row justify-between items-center">
          <div className="text-gray-400 text-sm mb-4 md:mb-0">
            {t('footer.legal.copyright')}
          </div>

          <div className="flex items-center space-x-6">
            {/* <a href="#" className="text-gray-400 hover:text-nova-red transition-colors duration-200 text-sm">
              {t('footer.legal.privacyPolicy')}
            </a>
            <a href="#" className="text-gray-400 hover:text-nova-red transition-colors duration-200 text-sm">
              {t('footer.legal.termsOfService')}
            </a> */}
            <button
              onClick={scrollToTop}
              className="bg-nova-red text-white p-2 rounded-lg hover:bg-opacity-90 transition-all duration-200 group"
              aria-label="Scroll to top"
            >
              <ArrowUp size={16} className="group-hover:-translate-y-1 transition-transform duration-200" />
            </button>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
