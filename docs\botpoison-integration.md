# BotPoison Integration Documentation

## Overview

This document describes the integration of BotPoison browser-based CAPTCHA challenge into the Novalead contact form. BotPoison provides invisible, user-friendly anti-spam protection that works seamlessly with Formspark form handling.

## Features

- **Invisible Challenge**: BotPoison runs challenges in the background without disrupting user experience
- **Formspark Integration**: Seamlessly integrates with existing Formspark endpoint
- **Multi-language Support**: Challenge status messages in English, French, and Dutch
- **Accessible Design**: Follows WCAG guidelines with proper ARIA labels and keyboard navigation
- **Loading States**: Visual feedback during challenge processing
- **Error Handling**: Graceful error recovery with user-friendly messages
- **Novalead Design**: Consistent with existing design system using Tailwind CSS

## Implementation Details

### Dependencies

- `@botpoison/browser`: Browser-based BotPoison client library
- Existing dependencies: React, react-i18next, lucide-react, tailwindcss

### Key Components

#### State Management
```typescript
const [captchaLoading, setCaptchaLoading] = useState(false);
const [captchaError, setCaptchaError] = useState<string | null>(null);
const [captchaSolution, setCaptchaSolution] = useState<string | null>(null);
const botpoisonRef = useRef<any>(null);
```

#### Challenge Processing
- Automatic challenge initiation on form submission
- Progress tracking with visual feedback
- Solution caching to prevent duplicate challenges
- Error recovery with retry capability

#### Form Integration
- Challenge solution included in form data as `_botpoison` field
- Form submission blocked until challenge completion
- Automatic solution reset after successful submission

### User Experience Flow

1. **Form Filling**: User fills out contact form normally
2. **Submit Attempt**: User clicks submit button
3. **Challenge Processing**: BotPoison challenge runs automatically in background
4. **Visual Feedback**: Loading spinner and status messages shown
5. **Challenge Completion**: Success indicator displayed
6. **Form Submission**: Form data sent to Formspark with challenge solution
7. **Success/Error**: Appropriate feedback shown to user

### Security Features

- Challenge solution validation on server side (handled by Formspark)
- Solution tokens are single-use and time-limited
- No sensitive data exposed to client-side
- Automatic retry on challenge failure

## Configuration

### BotPoison Setup

1. **Create Account**: Go to [https://botpoison.com/start/](https://botpoison.com/start/)
2. **Get Keys**: Create a new configuration and obtain public/secret keys
3. **Configure Public Key**: Replace `pk_xxxxxxxx` in Contact.tsx with your public key
4. **Configure Formspark**: Add secret key to Formspark form settings under Spam Protection

### Environment Variables (Recommended)

For production deployment, store the public key in environment variables:

```typescript
const publicKey = process.env.REACT_APP_BOTPOISON_PUBLIC_KEY || 'pk_xxxxxxxx';
```

### Formspark Configuration

1. Open your Formspark dashboard
2. Navigate to your form settings
3. Under "Spam Protection", select "Botpoison"
4. Paste your BotPoison secret key
5. Save settings

## Translation Keys

The following translation keys were added to support the BotPoison integration:

```json
{
  "contact": {
    "form": {
      "captcha": {
        "loading": "Loading security verification...",
        "processing": "Processing security challenge...",
        "error": "Security verification failed. Please try again.",
        "required": "Please complete the security verification before submitting."
      }
    }
  }
}
```

## Accessibility Features

- **Screen Reader Support**: All status messages are announced to screen readers
- **Keyboard Navigation**: All interactive elements are keyboard accessible
- **Visual Indicators**: Clear visual feedback for all states
- **Error Messages**: Descriptive error messages for troubleshooting
- **Loading States**: Clear indication when processing is occurring

## Error Handling

### Common Scenarios

1. **Network Issues**: Automatic retry with user notification
2. **Invalid Public Key**: Clear error message with setup instructions
3. **Challenge Timeout**: Automatic retry option
4. **Server Errors**: Form submission retry with fresh challenge

### Error Recovery

- Automatic solution reset on submission failure
- Clear error messages with actionable instructions
- Retry capability without page refresh
- Fallback to manual retry if automatic retry fails

## Testing

### Manual Testing Checklist

- [ ] Form submits successfully with valid data
- [ ] Challenge processes without user intervention
- [ ] Loading states display correctly
- [ ] Error states handle gracefully
- [ ] Success message appears after submission
- [ ] Form resets properly after submission
- [ ] Multiple submissions work correctly
- [ ] All languages display correct messages

### Browser Compatibility

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Troubleshooting

### Common Issues

1. **"pk_xxxxxxxx" Error**: Replace placeholder with actual public key
2. **Challenge Not Loading**: Check network connectivity and public key validity
3. **Form Not Submitting**: Verify Formspark secret key configuration
4. **Translation Missing**: Ensure all language files include captcha keys

### Debug Mode

Enable debug logging by adding to browser console:
```javascript
localStorage.setItem('botpoison-debug', 'true');
```

## Performance Considerations

- BotPoison library loads asynchronously
- Challenge processing typically takes 1-3 seconds
- No impact on initial page load
- Minimal memory footprint
- Automatic cleanup on component unmount

## Security Considerations

- Public key is safe to expose in client-side code
- Secret key must remain secure on server side
- Challenge solutions are single-use tokens
- No user data is sent to BotPoison servers
- HTTPS required for production use
