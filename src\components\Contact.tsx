import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Mail, Phone, MapPin, Send, CheckCircle, Shield, AlertCircle } from 'lucide-react';
import Botpoison from '@botpoison/browser';

// Initialize BotPoison instance with public key
const botpoison = new Botpoison({
  publicKey: import.meta.env.VITE_BOTPOISON_PUBLIC_KEY
});

const Contact: React.FC = () => {
  const { t } = useTranslation();
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const timeoutRef = useRef<number | null>(null);
  const formRef = useRef<HTMLFormElement>(null);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Reset all form states
  const resetFormStates = () => {
    setIsSubmitting(false);
    setSubmitError(null);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Prevent multiple submissions
    if (isSubmitting) {
      return;
    }

    // Capture form element BEFORE async operations
    const formElement = e.currentTarget as HTMLFormElement;

    if (!formElement || !(formElement instanceof HTMLFormElement)) {
      console.error('Invalid form element');
      setSubmitError('Form error. Please refresh the page and try again.');
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // First, collect form data
      const formData = new FormData(formElement);

      // Convert FormData to JSON object for Formspark AJAX submission
      const data: Record<string, string> = {};
      formData.forEach((value, key) => {
        // Skip the hidden _redirect field since it's not needed for AJAX
        if (key !== '_redirect') {
          data[key] = value.toString();
        }
      });

      // Process BotPoison challenge (following demo pattern)
      const { solution } = await botpoison.challenge();

      // Add BotPoison solution to the form data
      data._botpoison = solution;

      const response = await fetch('https://submit-form.com/3fkvBgrBg', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        // Success - show success message
        setIsSubmitted(true);
        formElement.reset(); // Clear the form

        // Clear any existing timeout
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }

        // Reset success state after 5 seconds to allow resubmission
        timeoutRef.current = window.setTimeout(() => {
          setIsSubmitted(false);
          resetFormStates();
        }, 5000);
      } else {
        // Handle HTTP error responses
        const errorText = await response.text().catch(() => 'Unknown error');
        throw new Error(`Form submission failed: ${response.status} ${errorText}`);
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      setSubmitError(t('contact.form.error') || 'An error occurred while submitting the form. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="contact" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            {t('contact.title.getIn')}{' '}
            <span className="gradient-nova-text">
              {t('contact.title.touch')}
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {t('contact.description')}
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div className="space-y-8">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                {t('contact.conversation.title')}
              </h3>
              <p className="text-gray-600 mb-8 leading-relaxed">
                {t('contact.conversation.description')}
              </p>
            </div>

            {/* Contact Details */}
            <div className="space-y-6">
              <div className="flex items-center">
                <div className="bg-nova-red rounded-lg p-3 mr-4">
                  <Phone className="text-white" size={24} />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">{t('contact.contactInfo.phone')}</h4>
                  <p className="text-gray-600">+32 56 90 24 91</p>
                </div>
              </div>

              <div className="flex items-center">
                <div className="bg-nova-blue rounded-lg p-3 mr-4">
                  <Mail className="text-white" size={24} />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">{t('contact.contactInfo.email')}</h4>
                  <p className="text-gray-600"><EMAIL></p>
                </div>
              </div>

              <div className="flex items-center">
                <div className="bg-nova-orange rounded-lg p-3 mr-4">
                  <MapPin className="text-white" size={24} />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">{t('contact.contactInfo.officeBE')}</h4>
                  <p className="text-gray-600">Henri Lebbestraat 188<br />8790 Waregem, België</p>
                </div>
              </div>

              <div className="flex items-center">
                <div className="bg-nova-orange rounded-lg p-3 mr-4">
                  <MapPin className="text-white" size={24} />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">{t('contact.contactInfo.officeMA')}</h4>
                  <p className="text-gray-600">Les portes de Marrakech Zone46<br />Marrakech, Marokko</p>
                </div>
              </div>
            </div>

            {/* Business Hours */}
            <div className="bg-gray-50 rounded-xl p-6">
              <h4 className="font-semibold text-gray-900 mb-4">{t('contact.businessHours.title')}</h4>
              <div className="space-y-2 text-gray-600">
                <div className="flex justify-between">
                  <span>{t('contact.businessHours.mondayFriday')}</span>
                  <span>{t('contact.businessHours.timeWeekdays')}</span>
                </div>
                <div className="flex justify-between">
                  <span>{t('contact.businessHours.saturday')}</span>
                  <span>{t('contact.businessHours.timeSaturday')}</span>
                </div>
                <div className="flex justify-between">
                  <span>{t('contact.businessHours.sunday')}</span>
                  <span>{t('contact.businessHours.closed')}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="bg-gray-50 rounded-2xl p-8">
            {!isSubmitted ? (
              <form ref={formRef} onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    {t('contact.form.fullName')} *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    disabled={isSubmitting}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nova-red focus:border-transparent transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    placeholder={t('contact.form.placeholders.fullName')}
                  />
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    {t('contact.form.emailAddress')} *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    disabled={isSubmitting}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nova-red focus:border-transparent transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    placeholder={t('contact.form.placeholders.email')}
                  />
                </div>

                <div>
                  <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-2">
                    {t('contact.form.companyName')}
                  </label>
                  <input
                    type="text"
                    id="company"
                    name="company"
                    disabled={isSubmitting}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nova-red focus:border-transparent transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    placeholder={t('contact.form.placeholders.company')}
                  />
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                    {t('contact.form.message')} *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    required
                    rows={5}
                    disabled={isSubmitting}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nova-red focus:border-transparent transition-all duration-200 resize-none disabled:opacity-50 disabled:cursor-not-allowed"
                    placeholder={t('contact.form.placeholders.message')}
                  />
                </div>

                {/* Security Notice */}
                <div className="flex items-center space-x-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <Shield className="text-nova-blue flex-shrink-0" size={16} />
                  <span className="text-sm text-blue-700">
                    This form is protected by anti-spam verification
                  </span>
                </div>

                {/* Error Message */}
                {submitError && (
                  <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <AlertCircle className="text-red-500 flex-shrink-0" size={16} />
                    <span className="text-sm text-red-700">{submitError}</span>
                  </div>
                )}

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-nova-red text-white py-4 px-6 rounded-lg hover:bg-opacity-90 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl flex items-center justify-center group disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-nova-red"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      {t('contact.form.sending')}
                    </>
                  ) : (
                    <>
                      {t('contact.form.sendMessage')}
                      <Send className="ml-2 group-hover:translate-x-1 transition-transform duration-200" size={20} />
                    </>
                  )}
                </button>
              </form>
            ) : (
              <div className="text-center py-12">
                <CheckCircle className="text-green-500 mx-auto mb-4" size={64} />
                <h3 className="text-2xl font-bold text-gray-900 mb-2">{t('contact.form.success.title')}</h3>
                <p className="text-gray-600 mb-6">
                  {t('contact.form.success.description')}
                </p>
                <button
                  onClick={() => {
                    setIsSubmitted(false);
                    resetFormStates();
                  }}
                  className="bg-nova-blue text-white py-2 px-6 rounded-lg hover:bg-opacity-90 transition-all duration-200 font-medium"
                >
                  Send Another Message
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
